"""
智能问题建议系统 - 基于PandasAI v3
根据数据特征和类型生成智能问题建议
"""
import pandas as pd
from typing import List, Dict, Any
from config_v3 import ConfigV3

class SmartQuestionSuggester:
    """智能问题建议器 - 基于数据内容和类型"""
    
    def __init__(self):
        self.financial_templates = {
            "revenue": {
                "basic": [
                    "显示收入数据的前10行",
                    "计算总收入",
                    "显示收入统计摘要",
                    "哪个时期收入最高？"
                ],
                "analysis": [
                    "分析收入趋势",
                    "按类别分析收入分布",
                    "计算收入增长率",
                    "找出收入异常值",
                    "预测下个月收入",
                    "创建收入趋势图"
                ],
                "comparison": [
                    "对比不同产品的收入",
                    "按地区比较收入表现",
                    "分析季节性收入模式",
                    "收入与目标的对比分析"
                ]
            },
            "expenses": {
                "basic": [
                    "显示支出数据概览",
                    "计算总支出",
                    "支出数据统计摘要",
                    "最大的支出项目是什么？"
                ],
                "analysis": [
                    "分析支出趋势",
                    "按部门分析支出",
                    "找出支出异常值",
                    "计算支出增长率",
                    "支出类别分布分析",
                    "创建支出分布图"
                ],
                "optimization": [
                    "识别可优化的支出项目",
                    "分析支出效率",
                    "对比预算与实际支出",
                    "支出控制建议"
                ]
            },
            "balance_sheet": {
                "basic": [
                    "显示资产负债表概览",
                    "计算总资产",
                    "资产负债表统计摘要",
                    "资产负债率是多少？"
                ],
                "ratios": [
                    "计算流动比率",
                    "计算资产负债率",
                    "计算权益比率",
                    "分析财务健康状况",
                    "资产结构分析",
                    "负债结构分析"
                ],
                "trends": [
                    "资产变化趋势",
                    "负债变化趋势",
                    "权益变化趋势",
                    "财务比率趋势分析"
                ]
            },
            "general": {
                "basic": [
                    "显示数据概览",
                    "数据有多少行和列？",
                    "显示前10行数据",
                    "数据统计摘要"
                ],
                "analysis": [
                    "找出数据中的模式",
                    "检测异常值",
                    "分析数据分布",
                    "计算相关性",
                    "创建数据可视化",
                    "生成数据洞察"
                ],
                "quality": [
                    "检查数据质量",
                    "找出缺失值",
                    "数据类型分析",
                    "数据完整性检查"
                ]
            }
        }
    
    def suggest_questions(self, df: pd.DataFrame, dataset_type: str = "general", 
                         context: str = "basic") -> List[str]:
        """
        基于数据特征生成智能问题建议
        
        Args:
            df: 数据框
            dataset_type: 数据集类型
            context: 上下文类型 (basic, analysis, comparison等)
        
        Returns:
            问题建议列表
        """
        suggestions = []
        
        # 获取基础模板
        templates = self.financial_templates.get(dataset_type, self.financial_templates["general"])
        base_suggestions = templates.get(context, templates.get("basic", []))
        
        # 添加基础建议
        suggestions.extend(base_suggestions)
        
        # 基于数据特征生成动态建议
        dynamic_suggestions = self._generate_dynamic_suggestions(df, dataset_type)
        suggestions.extend(dynamic_suggestions)
        
        # 去重并限制数量
        unique_suggestions = list(dict.fromkeys(suggestions))
        return unique_suggestions[:8]  # 返回最多8个建议
    
    def _generate_dynamic_suggestions(self, df: pd.DataFrame, dataset_type: str) -> List[str]:
        """基于数据特征生成动态建议"""
        suggestions = []
        columns = df.columns.tolist()
        
        # 检测日期列
        date_columns = self._detect_date_columns(df)
        if date_columns:
            suggestions.append(f"分析{date_columns[0]}的时间趋势")
            suggestions.append(f"按{date_columns[0]}分组统计")
        
        # 检测数值列
        numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
        if len(numeric_columns) > 1:
            suggestions.append(f"分析{numeric_columns[0]}和{numeric_columns[1]}的关系")
            suggestions.append(f"创建{numeric_columns[0]}的分布图")
        
        # 检测分类列
        categorical_columns = df.select_dtypes(include=['object']).columns.tolist()
        if categorical_columns and numeric_columns:
            suggestions.append(f"按{categorical_columns[0]}分组分析{numeric_columns[0]}")
        
        # 基于列名生成特定建议
        column_based_suggestions = self._generate_column_based_suggestions(columns, dataset_type)
        suggestions.extend(column_based_suggestions)
        
        return suggestions
    
    def _detect_date_columns(self, df: pd.DataFrame) -> List[str]:
        """检测日期列"""
        date_columns = []
        
        for col in df.columns:
            # 检查列名
            if any(keyword in col.lower() for keyword in ['date', 'time', '日期', '时间']):
                date_columns.append(col)
            # 检查数据类型
            elif pd.api.types.is_datetime64_any_dtype(df[col]):
                date_columns.append(col)
        
        return date_columns
    
    def _generate_column_based_suggestions(self, columns: List[str], dataset_type: str) -> List[str]:
        """基于列名生成建议"""
        suggestions = []
        
        # 财务关键词映射
        financial_keywords = {
            'revenue': ['收入', 'income', 'sales', '销售'],
            'expense': ['支出', 'cost', 'expense', '成本'],
            'profit': ['利润', 'profit', 'margin'],
            'price': ['价格', 'price', 'amount', '金额'],
            'quantity': ['数量', 'quantity', 'count'],
            'category': ['类别', 'category', 'type', '类型'],
            'region': ['地区', 'region', 'area', '区域'],
            'product': ['产品', 'product', 'item'],
            'customer': ['客户', 'customer', 'client'],
            'department': ['部门', 'department', 'division']
        }
        
        # 根据检测到的关键词生成建议
        for col in columns:
            col_lower = col.lower()
            
            for keyword, terms in financial_keywords.items():
                if any(term in col_lower for term in terms):
                    if keyword == 'revenue':
                        suggestions.append(f"分析{col}的增长趋势")
                        suggestions.append(f"找出{col}最高的记录")
                    elif keyword == 'expense':
                        suggestions.append(f"分析{col}的支出模式")
                        suggestions.append(f"找出{col}的异常支出")
                    elif keyword == 'category':
                        suggestions.append(f"按{col}分组统计")
                        suggestions.append(f"显示{col}的分布")
                    elif keyword == 'region':
                        suggestions.append(f"按{col}分析地区表现")
                    elif keyword == 'product':
                        suggestions.append(f"分析{col}的表现排名")
                    break
        
        return suggestions
    
    def get_contextual_suggestions(self, df: pd.DataFrame, dataset_type: str, 
                                 conversation_history: List[Dict]) -> List[str]:
        """
        基于对话历史生成上下文相关的建议
        
        Args:
            df: 数据框
            dataset_type: 数据集类型
            conversation_history: 对话历史
        
        Returns:
            上下文相关的问题建议
        """
        # 分析对话历史中的关键词
        recent_topics = self._extract_topics_from_history(conversation_history)
        
        # 基于最近话题生成相关建议
        if '趋势' in recent_topics or 'trend' in recent_topics:
            context = "analysis"
        elif '对比' in recent_topics or 'compare' in recent_topics:
            context = "comparison"
        elif '异常' in recent_topics or 'anomaly' in recent_topics:
            context = "quality"
        else:
            context = "basic"
        
        return self.suggest_questions(df, dataset_type, context)
    
    def _extract_topics_from_history(self, conversation_history: List[Dict]) -> List[str]:
        """从对话历史中提取话题关键词"""
        topics = []
        
        # 获取最近几条消息
        recent_messages = conversation_history[-3:] if len(conversation_history) > 3 else conversation_history
        
        for message in recent_messages:
            if message.get("role") == "user":
                content = message.get("content", "").lower()
                
                # 提取关键词
                keywords = ['趋势', 'trend', '对比', 'compare', '异常', 'anomaly', 
                           '分析', 'analysis', '统计', 'statistics', '图表', 'chart']
                
                for keyword in keywords:
                    if keyword in content:
                        topics.append(keyword)
        
        return topics
    
    def get_advanced_suggestions(self, df: pd.DataFrame, dataset_type: str) -> List[str]:
        """获取高级分析建议"""
        suggestions = []
        
        # 基于数据集类型的高级建议
        if dataset_type == "revenue":
            suggestions.extend([
                "进行收入预测分析",
                "识别收入增长驱动因素",
                "分析收入的季节性模式",
                "计算收入的变异系数",
                "进行收入敏感性分析"
            ])
        elif dataset_type == "expenses":
            suggestions.extend([
                "进行成本效益分析",
                "识别成本优化机会",
                "分析支出的弹性",
                "进行预算偏差分析",
                "成本结构优化建议"
            ])
        elif dataset_type == "balance_sheet":
            suggestions.extend([
                "进行杜邦分析",
                "计算Z-Score财务健康评分",
                "分析资本结构优化",
                "进行流动性风险评估",
                "资产配置效率分析"
            ])
        
        # 基于数据特征的高级建议
        if len(df) > 100:  # 大数据集
            suggestions.append("进行时间序列分解分析")
            suggestions.append("使用机器学习进行模式识别")
        
        if df.select_dtypes(include=['number']).shape[1] > 3:  # 多数值列
            suggestions.append("进行主成分分析(PCA)")
            suggestions.append("构建多元回归模型")
        
        return suggestions[:5]  # 返回最多5个高级建议
