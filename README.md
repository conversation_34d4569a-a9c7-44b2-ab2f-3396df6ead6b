# 智能数据分析平台

基于PandasAI和阿里云千问大模型的智能数据分析Web应用，支持自然语言查询、数据可视化和连续对话。

## ✨ 功能特性

- 🤖 **自然语言查询**: 使用中文自然语言进行数据分析
- 📊 **智能可视化**: 自动生成图表和统计结果
- 💬 **连续对话**: 支持上下文相关的连续提问
- 📁 **文件管理**: 支持CSV、Excel文件上传和持久化存储
- 🎯 **智能引导**: 自动识别并引导用户提出数据相关问题
- 🔒 **安全可靠**: 完善的错误处理和日志记录

## 🚀 快速开始

### 环境要求

- Python 3.9+
- 阿里云千问API密钥

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 财务数据分析
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，填入您的API密钥
QWEN_API_KEY=your_qwen_api_key_here
```

4. **启动应用**
```bash
streamlit run app.py
```

5. **访问应用**
打开浏览器访问 `http://localhost:8501`

## 📖 使用指南

### 1. 上传数据文件
- 在左侧边栏点击"上传数据文件"
- 支持CSV、Excel格式
- 文件会自动保存，下次可直接选择使用

### 2. 开始分析
选择文件后，您可以用自然语言提问，例如：

**基础查询**
- "显示前10行数据"
- "数据有多少行多少列？"
- "显示所有列名"

**统计分析**
- "计算销售额的平均值"
- "哪个产品销量最高？"
- "按地区统计销售总额"

**数据可视化**
- "绘制销售额趋势图"
- "制作产品销量柱状图"
- "显示各地区销售分布饼图"

**高级分析**
- "找出销售额异常的记录"
- "分析销售额与价格的相关性"
- "预测下个月的销售趋势"

### 3. 连续对话
系统支持上下文理解，您可以基于前面的结果继续提问：
- "再显示详细数据"
- "换个颜色重新绘制"
- "只看前三名的数据"

## 🏗️ 项目结构

```
财务数据分析/
├── app.py                 # 主应用程序
├── config.py             # 配置管理
├── requirements.txt      # 依赖包列表
├── .env.example         # 环境变量模板
├── core/                # 核心模块
│   ├── data_manager.py  # 数据管理
│   ├── llm_manager.py   # 大模型管理
│   └── analysis_engine.py # 分析引擎
├── utils/               # 工具模块
│   ├── logger.py        # 日志工具
│   └── error_handler.py # 错误处理
└── uploaded_files/      # 上传文件存储目录
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `QWEN_API_KEY` | 千问API密钥 | 必填 |
| `QWEN_BASE_URL` | API基础URL | https://dashscope.aliyuncs.com/compatible-mode/v1 |
| `APP_TITLE` | 应用标题 | 智能数据分析平台 |
| `UPLOAD_DIRECTORY` | 文件上传目录 | uploaded_files |
| `MAX_FILE_SIZE_MB` | 最大文件大小(MB) | 100 |
| `LOG_LEVEL` | 日志级别 | INFO |

### 获取千问API密钥

1. 访问 [阿里云控制台](https://dashscope.console.aliyun.com/)
2. 开通千问服务
3. 创建API密钥
4. 将密钥配置到`.env`文件中

## 🔧 开发指南

### 添加新功能

1. **扩展数据源**: 在`DataManager`中添加新的文件格式支持
2. **自定义分析**: 在`AnalysisEngine`中添加专门的分析方法
3. **界面定制**: 修改`app.py`中的Streamlit组件

### 调试技巧

- 查看日志文件 `app.log` 了解详细错误信息
- 使用Streamlit的调试模式: `streamlit run app.py --logger.level=debug`
- 在代码中添加 `st.write()` 输出调试信息

## 🚀 部署方案

### Streamlit Cloud (推荐)

1. 将代码推送到GitHub
2. 访问 [Streamlit Cloud](https://streamlit.io/cloud)
3. 连接GitHub仓库
4. 在Secrets中配置环境变量
5. 一键部署

### Docker部署

```bash
# 构建镜像
docker build -t data-analysis-app .

# 运行容器
docker run -p 8501:8501 -e QWEN_API_KEY=your_key data-analysis-app
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 常见问题

**Q: API调用失败怎么办？**
A: 检查API密钥是否正确配置，网络连接是否正常

**Q: 文件上传失败？**
A: 确认文件格式支持，文件大小不超过限制

**Q: 分析结果不准确？**
A: 尝试更具体的问题描述，或检查数据质量

**Q: 如何清除对话历史？**
A: 重新选择文件会自动清空对话历史

---

如有其他问题，请提交Issue或联系开发团队。
