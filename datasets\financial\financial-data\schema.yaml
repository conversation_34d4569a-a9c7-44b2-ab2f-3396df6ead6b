name: financial_data
source:
  type: parquet
  path: data.parquet
description: "\u901A\u7528\u8D22\u52A1\u6570\u636E\u5206\u6790"
columns:
- name: "\u516C\u53F8"
  type: string
  description: "\u6570\u636E\u5B57\u6BB5 - \u516C\u53F8"
- name: "\u5E74\u4EFD"
  type: integer
  description: "\u6570\u636E\u5B57\u6BB5 - \u5E74\u4EFD"
- name: "\u6708\u4EFD"
  type: integer
  description: "\u6570\u636E\u5B57\u6BB5 - \u6708\u4EFD"
- name: "\u8D22\u62A5\u7C7B\u578B"
  type: string
  description: "\u6570\u636E\u5B57\u6BB5 - \u8D22\u62A5\u7C7B\u578B"
- name: "\u8D22\u62A5\u9879\u76EE"
  type: string
  description: "\u6570\u636E\u5B57\u6BB5 - \u8D22\u62A5\u9879\u76EE"
- name: "\u6570\u636E\u7C7B\u578B"
  type: string
  description: "\u6570\u636E\u5B57\u6BB5 - \u6570\u636E\u7C7B\u578B"
- name: "\u6570\u636E"
  type: float
  description: "\u6570\u636E\u5B57\u6BB5 - \u6570\u636E"
