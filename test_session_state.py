#!/usr/bin/env python3
"""
测试会话状态中的分析引擎持久化
"""
import streamlit as st
import pandas as pd
from core.analysis_engine_v3 import AnalysisEngineV3

def test_session_state():
    """测试会话状态管理"""
    print("🧪 测试会话状态中的分析引擎持久化...")
    
    # 模拟Streamlit会话状态
    class MockSessionState:
        def __init__(self):
            self.data = {}
        
        def __contains__(self, key):
            return key in self.data
        
        def __getitem__(self, key):
            return self.data[key]
        
        def __setitem__(self, key, value):
            self.data[key] = value
    
    # 创建模拟会话状态
    session_state = MockSessionState()
    
    # 第一次创建分析引擎
    print("1. 第一次创建分析引擎...")
    if 'analysis_engine' not in session_state:
        session_state['analysis_engine'] = AnalysisEngineV3()
    engine1 = session_state['analysis_engine']
    print(f"   引擎1 ID: {id(engine1)}")
    
    # 加载测试数据
    test_data = pd.DataFrame({
        'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
        'revenue': [10000, 12000, 11000],
        'category': ['产品A', '产品B', '产品A']
    })
    
    success = engine1.load_data(test_data, 'test-data', 'general')
    print(f"   数据加载成功: {success}")
    print(f"   数据形状: {engine1.current_data.shape if engine1.current_data is not None else 'None'}")
    
    # 第二次获取分析引擎（模拟页面刷新）
    print("2. 第二次获取分析引擎（模拟页面刷新）...")
    if 'analysis_engine' not in session_state:
        session_state['analysis_engine'] = AnalysisEngineV3()
    engine2 = session_state['analysis_engine']
    print(f"   引擎2 ID: {id(engine2)}")
    print(f"   引擎是否相同: {engine1 is engine2}")
    print(f"   数据是否保持: {engine2.current_data.shape if engine2.current_data is not None else 'None'}")
    
    # 验证数据一致性
    if engine1.current_data is not None and engine2.current_data is not None:
        data_equal = engine1.current_data.equals(engine2.current_data)
        print(f"   数据内容是否一致: {data_equal}")
    
    print("✅ 会话状态测试完成")

if __name__ == "__main__":
    test_session_state()
