#!/usr/bin/env python3
"""
深度诊断脚本 - 检查实际运行时的问题
"""
import sys
import os
import traceback
import pandas as pd
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试所有导入"""
    print("🔍 测试模块导入...")
    
    try:
        print("   导入 config_v3...")
        from config_v3 import ConfigV3
        print(f"   ✅ ConfigV3 导入成功")
        print(f"      - API密钥状态: {'已设置' if ConfigV3.DASHSCOPE_API_KEY else '未设置'}")
        print(f"      - LLM模型: {ConfigV3.LLM_MODEL}")
    except Exception as e:
        print(f"   ❌ ConfigV3 导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("   导入 utils.logger...")
        from utils.logger import setup_logger, get_logger
        setup_logger()
        logger = get_logger(__name__)
        print(f"   ✅ Logger 导入成功")
    except Exception as e:
        print(f"   ❌ Logger 导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("   导入 core.data_manager...")
        from core.data_manager import DataManager
        print(f"   ✅ DataManager 导入成功")
    except Exception as e:
        print(f"   ❌ DataManager 导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("   导入 core.analysis_engine_v3...")
        from core.analysis_engine_v3 import AnalysisEngineV3
        print(f"   ✅ AnalysisEngineV3 导入成功")
    except Exception as e:
        print(f"   ❌ AnalysisEngineV3 导入失败: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_analysis_engine_step_by_step():
    """逐步测试分析引擎"""
    print("\n🧠 逐步测试分析引擎...")
    
    try:
        from core.analysis_engine_v3 import AnalysisEngineV3
        from config_v3 import ConfigV3
        
        print("   步骤1: 创建分析引擎实例...")
        engine = AnalysisEngineV3()
        print(f"   ✅ 实例创建成功")
        print(f"      - LLM配置状态: {engine.llm_configured}")
        print(f"      - current_data状态: {type(engine.current_data)}")
        print(f"      - 语义数据集数量: {len(engine.semantic_datasets)}")
        
        print("   步骤2: 创建测试数据...")
        test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'revenue': [10000, 12000, 11000],
            'category': ['产品A', '产品B', '产品A'],
            'region': ['北京', '上海', '广州']
        })
        print(f"   ✅ 测试数据创建成功，形状: {test_data.shape}")
        
        print("   步骤3: 调用load_data方法...")
        print(f"      - 传入参数: df.shape={test_data.shape}, dataset_name='test-revenue', dataset_type='revenue'")
        
        # 详细跟踪load_data调用
        result = engine.load_data(test_data, "test-revenue", "revenue")
        
        print(f"   ✅ load_data调用完成，返回值: {result}")
        print(f"      - current_data状态: {type(engine.current_data)}")
        if engine.current_data is not None:
            print(f"      - current_data形状: {engine.current_data.shape}")
            print(f"      - current_data列名: {list(engine.current_data.columns)}")
        print(f"      - 语义数据集: {list(engine.semantic_datasets.keys())}")
        
        return engine, result
        
    except Exception as e:
        print(f"   ❌ 分析引擎测试失败: {e}")
        traceback.print_exc()
        return None, False

def test_app_v3_import():
    """测试app_v3导入"""
    print("\n📱 测试app_v3导入...")
    
    try:
        print("   导入app_v3模块...")
        import app_v3
        print(f"   ✅ app_v3导入成功")
        
        print("   创建DataAnalysisAppV3实例...")
        app = app_v3.DataAnalysisAppV3()
        print(f"   ✅ 应用实例创建成功")
        print(f"      - 数据管理器: {type(app.data_manager)}")
        print(f"      - 分析引擎: {type(app.analysis_engine)}")
        print(f"      - 分析引擎LLM状态: {app.analysis_engine.llm_configured}")
        
        return app
        
    except Exception as e:
        print(f"   ❌ app_v3测试失败: {e}")
        traceback.print_exc()
        return None

def simulate_file_upload():
    """模拟文件上传过程"""
    print("\n📁 模拟文件上传过程...")
    
    try:
        from core.data_manager import DataManager
        from core.analysis_engine_v3 import AnalysisEngineV3
        
        # 创建测试CSV文件
        test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'revenue': [10000, 12000, 11000],
            'category': ['产品A', '产品B', '产品A'],
            'region': ['北京', '上海', '广州']
        })
        
        # 确保上传目录存在
        manager = DataManager()
        if not os.path.exists(manager.upload_dir):
            os.makedirs(manager.upload_dir)

        test_file = "test_revenue.csv"
        test_file_path = os.path.join(manager.upload_dir, test_file)
        test_data.to_csv(test_file_path, index=False, encoding='utf-8')
        print(f"   ✅ 测试文件创建: {test_file_path}")

        # 测试数据管理器加载
        df, error = manager.load_data(test_file)
        
        if error:
            print(f"   ❌ 数据管理器加载失败: {error}")
            return False
        
        print(f"   ✅ 数据管理器加载成功，形状: {df.shape}")
        
        # 测试分析引擎加载
        engine = AnalysisEngineV3()
        dataset_name = "test-revenue"
        dataset_type = "revenue"
        
        print(f"   调用engine.reload_data...")
        success = engine.reload_data(df, dataset_name, dataset_type)
        
        print(f"   reload_data结果: {success}")
        print(f"   engine.current_data: {type(engine.current_data)}")
        if engine.current_data is not None:
            print(f"   数据形状: {engine.current_data.shape}")
        
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        
        return success
        
    except Exception as e:
        print(f"   ❌ 文件上传模拟失败: {e}")
        traceback.print_exc()
        return False

def check_streamlit_session_state():
    """检查Streamlit会话状态相关问题"""
    print("\n🌐 检查Streamlit相关问题...")
    
    try:
        import streamlit as st
        print("   ✅ Streamlit导入成功")
        
        # 模拟会话状态
        class MockSessionState:
            def __init__(self):
                self.messages = []
                self.current_file = None
                self.data_loaded = False
                self.current_dataset_name = None
                self.dataset_type = "general"
                self.show_welcome = True
        
        mock_state = MockSessionState()
        print("   ✅ 模拟会话状态创建成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Streamlit检查失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主诊断函数"""
    print("🚀 开始深度诊断...")
    print("=" * 60)
    
    # 1. 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，停止诊断")
        return
    
    # 2. 逐步测试分析引擎
    engine, load_result = test_analysis_engine_step_by_step()
    if engine is None:
        print("\n❌ 分析引擎测试失败，停止诊断")
        return
    
    # 3. 测试app_v3导入
    app = test_app_v3_import()
    if app is None:
        print("\n❌ app_v3测试失败")
    
    # 4. 模拟文件上传
    upload_success = simulate_file_upload()
    if not upload_success:
        print("\n❌ 文件上传模拟失败")
    
    # 5. 检查Streamlit相关
    streamlit_ok = check_streamlit_session_state()
    
    print("\n" + "=" * 60)
    print("🎯 诊断总结:")
    print(f"   导入测试: ✅")
    print(f"   分析引擎: {'✅' if engine else '❌'}")
    print(f"   数据加载: {'✅' if load_result else '❌'}")
    print(f"   应用创建: {'✅' if app else '❌'}")
    print(f"   文件上传: {'✅' if upload_success else '❌'}")
    print(f"   Streamlit: {'✅' if streamlit_ok else '❌'}")
    
    if engine and engine.current_data is not None:
        print(f"\n✅ 核心问题已修复：数据能够正确加载到分析引擎")
    else:
        print(f"\n❌ 核心问题仍存在：数据无法加载到分析引擎")

if __name__ == "__main__":
    main()
