"""
数据管理模块
"""
import os
import pandas as pd
from typing import Optional, List, Tuple
from config_v3 import ConfigV3
from utils.logger import get_logger

logger = get_logger(__name__)

class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.upload_dir = ConfigV3.UPLOAD_DIRECTORY
        self.supported_types = ConfigV3.SUPPORTED_FILE_TYPES
        
    def get_uploaded_files(self) -> List[str]:
        """获取已上传的文件列表"""
        try:
            if not os.path.exists(self.upload_dir):
                return []
            
            files = []
            for file in os.listdir(self.upload_dir):
                if any(file.lower().endswith(f'.{ext}') for ext in self.supported_types):
                    files.append(file)
            
            return sorted(files)
        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return []
    
    def save_uploaded_file(self, file_content: bytes, filename: str) -> bool:
        """保存上传的文件"""
        try:
            # 确保上传目录存在
            if not os.path.exists(self.upload_dir):
                os.makedirs(self.upload_dir)
            
            file_path = os.path.join(self.upload_dir, filename)
            
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            logger.info(f"文件保存成功: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"文件保存失败: {e}")
            return False
    
    def load_data(self, filename: str) -> Tuple[Optional[pd.DataFrame], Optional[str]]:
        """
        加载数据文件
        
        Returns:
            Tuple[DataFrame, error_message]: 数据框和错误信息
        """
        try:
            file_path = os.path.join(self.upload_dir, filename)
            
            if not os.path.exists(file_path):
                return None, f"文件不存在: {filename}"
            
            # 根据文件扩展名选择读取方法
            if filename.lower().endswith('.csv'):
                # 尝试不同的编码
                for encoding in ['utf-8', 'gbk', 'gb2312']:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    return None, "无法读取CSV文件，编码格式不支持"
                    
            elif filename.lower().endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            else:
                return None, f"不支持的文件格式: {filename}"
            
            # 基本数据验证
            if df.empty:
                return None, "文件为空"
            
            logger.info(f"数据加载成功: {filename}, 形状: {df.shape}")
            return df, None
            
        except Exception as e:
            error_msg = f"加载文件失败: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    def get_data_info(self, df: pd.DataFrame) -> dict:
        """获取数据基本信息"""
        try:
            info = {
                'shape': df.shape,
                'columns': df.columns.tolist(),
                'dtypes': df.dtypes.to_dict(),
                'null_counts': df.isnull().sum().to_dict(),
                'memory_usage': df.memory_usage(deep=True).sum()
            }
            return info
        except Exception as e:
            logger.error(f"获取数据信息失败: {e}")
            return {}
