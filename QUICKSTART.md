# 快速启动指南

## 🚀 5分钟快速体验

### 1. 环境准备
```bash
# 确保Python 3.9+已安装
python --version

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置API密钥
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，填入您的千问API密钥
# QWEN_API_KEY=your_api_key_here
```

### 3. 启动应用
```bash
# 方式1：使用启动脚本（推荐）
python run.py

# 方式2：直接启动
streamlit run app.py
```

### 4. 开始使用
1. 打开浏览器访问 `http://localhost:8501`
2. 上传示例数据文件 `sample_data/sales_data.csv`
3. 开始提问，例如：
   - "显示前5行数据"
   - "哪个地区的销售额最高？"
   - "绘制各产品销量柱状图"

## 🔧 故障排除

### 常见问题

**Q: 模块导入错误**
```bash
# 解决方案：重新安装依赖
pip install -r requirements.txt --upgrade
```

**Q: API调用失败**
- 检查.env文件中的API密钥是否正确
- 确认网络连接正常
- 验证API密钥是否有效

**Q: 文件上传失败**
- 确认文件格式为CSV或Excel
- 检查文件大小是否超过限制
- 确认文件编码为UTF-8

### 系统检查
```bash
# 运行系统检查脚本
python test_app.py
```

## 📊 示例查询

使用示例数据文件，您可以尝试以下查询：

### 基础查询
- "数据有多少行多少列？"
- "显示所有列名"
- "显示前10行数据"

### 统计分析
- "计算总销售额"
- "哪个销售员业绩最好？"
- "各地区销售额排名"

### 数据可视化
- "绘制销售额趋势图"
- "制作产品销量饼图"
- "显示各地区销售分布"

### 高级分析
- "找出销售额最高的前3个产品"
- "分析销售额与销量的关系"
- "按地区统计平均销售额"

## 🎯 下一步

- 上传您自己的数据文件
- 探索更多分析功能
- 查看完整文档 [README.md](README.md)
- 自定义配置和扩展功能

---

如有问题，请查看 [README.md](README.md) 或提交Issue。
