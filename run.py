#!/usr/bin/env python3
"""
智能数据分析平台启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """检查环境要求"""
    print("🔍 检查环境要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print("❌ Python版本需要3.9或更高")
        return False
    
    # 检查requirements.txt
    if not Path("requirements.txt").exists():
        print("❌ 找不到requirements.txt文件")
        return False
    
    # 检查.env文件
    if not Path(".env").exists():
        print("⚠️  未找到.env文件，请复制.env.example并配置API密钥")
        return False
    
    print("✅ 环境检查通过")
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("📁 创建目录...")
    directories = ["uploaded_files", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 目录创建完成")

def start_app():
    """启动应用"""
    print("🚀 启动应用...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 应用启动失败: {e}")
    except KeyboardInterrupt:
        print("\n👋 应用已停止")

def main():
    """主函数"""
    print("=" * 50)
    print("🤖 智能数据分析平台")
    print("=" * 50)
    
    if not check_requirements():
        print("\n请解决上述问题后重新运行")
        return
    
    # 询问是否安装依赖
    install_deps = input("\n是否需要安装/更新依赖包？(y/N): ").lower().strip()
    if install_deps in ['y', 'yes']:
        if not install_dependencies():
            return
    
    create_directories()
    
    print("\n" + "=" * 50)
    print("📋 使用提示:")
    print("1. 确保已在.env文件中配置QWEN_API_KEY")
    print("2. 应用将在浏览器中自动打开")
    print("3. 使用Ctrl+C停止应用")
    print("=" * 50)
    
    input("\n按Enter键启动应用...")
    start_app()

if __name__ == "__main__":
    main()
