#!/usr/bin/env python3
"""
测试修复后的分析引擎
"""
from core.analysis_engine_v3 import AnalysisEngineV3
import pandas as pd

def main():
    print("🧠 测试修复后的分析引擎...")
    
    # 创建分析引擎
    engine = AnalysisEngineV3()
    print(f"LLM配置状态: {engine.llm_configured}")
    
    # 测试数据加载
    test_data = pd.DataFrame({
        'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
        'revenue': [10000, 12000, 11000],
        'category': ['产品A', '产品B', '产品A']
    })
    
    success = engine.load_data(test_data, 'test-revenue', 'revenue')
    print(f"数据加载成功: {success}")
    print(f"当前数据形状: {engine.current_data.shape if engine.current_data is not None else None}")
    print(f"语义数据集: {list(engine.semantic_datasets.keys())}")
    
    # 测试LLM连接
    result = engine.validate_llm_connection()
    print(f"LLM连接测试: {result['type']} - {result['content']}")
    
    # 测试简单分析
    if engine.llm_configured and engine.current_data is not None:
        analysis_result = engine.analyze('数据有多少行？', 'test-revenue')
        print(f"分析结果: {analysis_result['type']} - {analysis_result['content']}")

if __name__ == "__main__":
    main()
