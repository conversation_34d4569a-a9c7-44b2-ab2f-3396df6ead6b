#!/usr/bin/env python3
"""
测试PandasAI v3与LiteLLM
"""
import pandas as pd
import os
from dotenv import load_dotenv

def test_pandasai_v3_litellm():
    """测试PandasAI v3与LiteLLM"""
    print("=" * 60)
    print("测试PandasAI v3与LiteLLM")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    try:
        import pandasai as pai
        from pandasai_litellm.litellm import LiteLLM
        
        print(f"PandasAI版本: {pai.__version__.__version__}")
        
        # 创建测试数据
        df = pd.DataFrame({
            'name': ['Alice', 'Bob', '<PERSON>'],
            'age': [25, 30, 35],
            'salary': [50000, 60000, 70000]
        })
        
        print("测试数据:")
        print(df)
        
        # 获取API配置
        api_key = os.getenv('DASHSCOPE_API_KEY')
        base_url = os.getenv('OPENAI_API_BASE')

        print(f"\nAPI密钥: {api_key[:10]}...")
        print(f"Base URL: {base_url}")

        # 设置LiteLLM需要的环境变量
        os.environ['DASHSCOPE_API_KEY'] = api_key

        # 创建LiteLLM实例，使用Dashscope提供商
        llm = LiteLLM(
            model="dashscope/qwen-max",  # 使用dashscope前缀
            temperature=0.1,
            max_tokens=1000
        )
        
        print("✅ LiteLLM实例创建成功")
        
        # 配置PandasAI
        pai.config.set({
            "llm": llm,
            "temperature": 0.1
        })
        
        print("✅ PandasAI配置成功")
        
        # 创建PandasAI DataFrame
        pai_df = pai.DataFrame(df)
        print("✅ PandasAI DataFrame创建成功")
        
        # 测试简单查询
        question = "How many rows are in this data?"
        print(f"\n测试问题: {question}")
        
        try:
            result = pai_df.chat(question)
            print(f"✅ 查询成功！结果: {result}")
            return True
        except Exception as chat_error:
            print(f"❌ 查询失败: {chat_error}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chinese_query():
    """测试中文查询"""
    print("\n" + "=" * 60)
    print("测试中文查询")
    print("=" * 60)
    
    try:
        import pandasai as pai
        from pandasai_litellm.litellm import LiteLLM
        
        # 加载环境变量
        load_dotenv()
        
        # 创建中文测试数据
        df = pd.DataFrame({
            '姓名': ['张三', '李四', '王五'],
            '年龄': [25, 30, 35],
            '工资': [50000, 60000, 70000]
        })
        
        print("中文测试数据:")
        print(df)
        
        # 配置LLM
        api_key = os.getenv('DASHSCOPE_API_KEY')
        base_url = os.getenv('OPENAI_API_BASE')

        # 设置环境变量
        os.environ['DASHSCOPE_API_KEY'] = api_key

        llm = LiteLLM(
            model="dashscope/qwen-max",
            temperature=0.1,
            max_tokens=1000
        )
        
        pai.config.set({"llm": llm})
        
        # 创建DataFrame
        pai_df = pai.DataFrame(df)
        
        # 测试中文查询
        question = "这个数据有多少行？"
        print(f"\n测试问题: {question}")
        
        result = pai_df.chat(question)
        print(f"✅ 中文查询成功！结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 中文查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试PandasAI v3")
    
    # 测试基本功能
    basic_success = test_pandasai_v3_litellm()
    
    # 测试中文查询
    chinese_success = test_chinese_query()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if basic_success and chinese_success:
        print("🎉 所有测试通过！PandasAI v3配置成功。")
        return True
    else:
        print("💥 部分测试失败。")
        print(f"基本功能: {'✅' if basic_success else '❌'}")
        print(f"中文查询: {'✅' if chinese_success else '❌'}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
