#!/usr/bin/env python3
"""
测试基本功能（不依赖LLM）
"""
import pandas as pd
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config_v3 import ConfigV3
from core.analysis_engine_v3 import AnalysisEngineV3
from core.data_manager import DataManager
from utils.logger import setup_logger, get_logger

# 设置日志
setup_logger()
logger = get_logger(__name__)

def test_basic_config():
    """测试基本配置（不验证API密钥）"""
    print("🔧 测试基本配置...")
    try:
        # 创建必要目录
        directories = [
            ConfigV3.UPLOAD_DIRECTORY, 
            ConfigV3.EXPORTS_DIRECTORY, 
            ConfigV3.SEMANTIC_LAYER_PATH,
            f"{ConfigV3.EXPORTS_DIRECTORY}/charts"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
        
        print(f"✅ 基本配置成功")
        print(f"   - 上传目录: {ConfigV3.UPLOAD_DIRECTORY}")
        print(f"   - 导出目录: {ConfigV3.EXPORTS_DIRECTORY}")
        print(f"   - 语义层路径: {ConfigV3.SEMANTIC_LAYER_PATH}")
        print(f"   - LLM模型: {ConfigV3.LLM_MODEL}")
        return True
    except Exception as e:
        print(f"❌ 基本配置失败: {e}")
        return False

def test_data_manager():
    """测试数据管理器"""
    print("\n📁 测试数据管理器...")
    try:
        manager = DataManager()
        print(f"✅ 数据管理器初始化成功")
        print(f"   - 上传目录: {manager.upload_dir}")
        print(f"   - 支持格式: {manager.supported_types}")
        
        # 测试文件列表
        files = manager.get_uploaded_files()
        print(f"   - 已上传文件: {len(files)} 个")
        
        return manager
    except Exception as e:
        print(f"❌ 数据管理器测试失败: {e}")
        return None

def test_analysis_engine_basic():
    """测试分析引擎基本功能（不依赖LLM）"""
    print("\n🧠 测试分析引擎基本功能...")
    try:
        # 临时禁用API密钥以测试基本功能
        original_key = ConfigV3.DASHSCOPE_API_KEY
        ConfigV3.DASHSCOPE_API_KEY = ""
        
        engine = AnalysisEngineV3()
        print(f"✅ 分析引擎初始化成功")
        print(f"   - LLM配置状态: {'已配置' if engine.llm_configured else '未配置（预期）'}")
        
        # 恢复原始密钥
        ConfigV3.DASHSCOPE_API_KEY = original_key
        
        return engine
    except Exception as e:
        print(f"❌ 分析引擎基本测试失败: {e}")
        return None

def test_data_loading_without_llm(engine):
    """测试数据加载（不依赖LLM）"""
    print("\n📊 测试数据加载（无LLM模式）...")
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'revenue': [10000, 12000, 11000],
            'category': ['产品A', '产品B', '产品A'],
            'region': ['北京', '上海', '广州']
        })
        
        print(f"   - 测试数据形状: {test_data.shape}")
        
        # 测试数据加载
        success = engine.load_data(test_data, "test-dataset", "revenue")
        
        if success:
            print(f"✅ 数据加载成功")
            print(f"   - 引擎数据形状: {engine.current_data.shape if engine.current_data is not None else 'None'}")
            print(f"   - 语义数据集: {list(engine.semantic_datasets.keys())}")
            
            # 验证数据内容
            if engine.current_data is not None:
                print(f"   - 数据列: {list(engine.current_data.columns)}")
                print(f"   - 数据预览:")
                print(engine.current_data.head(2).to_string(index=False))
            
            return True
        else:
            print(f"❌ 数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        return False

def test_backup_analysis(engine):
    """测试备用分析功能"""
    print("\n📈 测试备用分析功能...")
    try:
        if engine.current_data is None:
            print("❌ 没有数据可供分析")
            return False
        
        # 测试备用分析方法
        test_questions = [
            "显示数据形状",
            "显示列名", 
            "显示前5行",
            "显示统计摘要"
        ]
        
        for question in test_questions:
            try:
                result = engine._backup_analysis(question)
                print(f"   ✅ '{question}': {result['type']}")
                if result['type'] == 'text':
                    print(f"      结果: {result['content'][:100]}...")
                elif result['type'] == 'dataframe':
                    print(f"      数据框形状: {result['content'].shape}")
            except Exception as e:
                print(f"   ❌ '{question}': {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 备用分析测试失败: {e}")
        return False

def test_data_summary(engine):
    """测试数据摘要功能"""
    print("\n📋 测试数据摘要功能...")
    try:
        result = engine.get_data_summary()
        
        if result["type"] == "error":
            print(f"❌ 数据摘要失败: {result['content']}")
            return False
        else:
            print(f"✅ 数据摘要成功")
            print(f"   摘要内容:")
            print(result['content'])
            return True
            
    except Exception as e:
        print(f"❌ 数据摘要测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试基本功能（不依赖LLM）...")
    
    # 测试基本配置
    if not test_basic_config():
        print("\n❌ 基本配置测试失败，停止测试")
        return
    
    # 测试数据管理器
    manager = test_data_manager()
    if manager is None:
        print("\n❌ 数据管理器测试失败，停止测试")
        return
    
    # 测试分析引擎基本功能
    engine = test_analysis_engine_basic()
    if engine is None:
        print("\n❌ 分析引擎基本测试失败，停止测试")
        return
    
    # 测试数据加载
    if not test_data_loading_without_llm(engine):
        print("\n❌ 数据加载测试失败")
        return
    
    # 测试备用分析
    if not test_backup_analysis(engine):
        print("\n❌ 备用分析测试失败")
    
    # 测试数据摘要
    if not test_data_summary(engine):
        print("\n❌ 数据摘要测试失败")
    
    print("\n🎉 基本功能测试完成！")
    print("\n📝 总结:")
    print("   ✅ 数据加载功能正常")
    print("   ✅ 备用分析功能正常") 
    print("   ✅ 数据摘要功能正常")
    print("   ⚠️  LLM功能需要有效的API密钥")

if __name__ == "__main__":
    main()
