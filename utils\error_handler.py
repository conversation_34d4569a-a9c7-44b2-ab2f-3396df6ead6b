"""
错误处理工具模块
"""
import traceback
import functools
from typing import Callable, Any
from utils.logger import get_logger

logger = get_logger(__name__)

def handle_exceptions(default_return=None, log_error=True):
    """
    装饰器：统一处理异常
    
    Args:
        default_return: 异常时的默认返回值
        log_error: 是否记录错误日志
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_error:
                    logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
                    logger.error(f"错误堆栈: {traceback.format_exc()}")
                return default_return
        return wrapper
    return decorator

def safe_execute(func: Callable, *args, **kwargs) -> tuple:
    """
    安全执行函数
    
    Returns:
        tuple: (success: bool, result: Any, error: str)
    """
    try:
        result = func(*args, **kwargs)
        return True, result, None
    except Exception as e:
        error_msg = str(e)
        logger.error(f"安全执行失败: {error_msg}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return False, None, error_msg

class ErrorMessages:
    """错误消息常量"""
    
    # 文件相关错误
    FILE_NOT_FOUND = "文件不存在，请检查文件路径"
    FILE_UPLOAD_FAILED = "文件上传失败，请重试"
    FILE_FORMAT_UNSUPPORTED = "不支持的文件格式"
    FILE_TOO_LARGE = "文件过大，请上传小于{}MB的文件"
    FILE_CORRUPTED = "文件损坏或格式错误"
    
    # 数据相关错误
    DATA_EMPTY = "数据文件为空"
    DATA_LOAD_FAILED = "数据加载失败"
    DATA_ANALYSIS_FAILED = "数据分析失败"
    
    # API相关错误
    API_KEY_MISSING = "API密钥未配置"
    API_CALL_FAILED = "API调用失败"
    API_QUOTA_EXCEEDED = "API调用次数超限"
    
    # 通用错误
    UNKNOWN_ERROR = "发生未知错误"
    NETWORK_ERROR = "网络连接错误"
    TIMEOUT_ERROR = "操作超时"
    
    @classmethod
    def get_user_friendly_message(cls, error: Exception) -> str:
        """获取用户友好的错误消息"""
        error_str = str(error).lower()
        
        if "file not found" in error_str or "no such file" in error_str:
            return cls.FILE_NOT_FOUND
        elif "permission denied" in error_str:
            return "文件访问权限不足"
        elif "timeout" in error_str:
            return cls.TIMEOUT_ERROR
        elif "network" in error_str or "connection" in error_str:
            return cls.NETWORK_ERROR
        elif "api" in error_str and "key" in error_str:
            return cls.API_KEY_MISSING
        elif "quota" in error_str or "limit" in error_str:
            return cls.API_QUOTA_EXCEEDED
        else:
            return f"操作失败: {str(error)}"
