name: test_dataset
source:
  type: parquet
  path: data.parquet
description: "\u6536\u5165\u6570\u636E\u5206\u6790 - \u5305\u542B\u6536\u5165\u76F8\
  \u5173\u7684\u8D22\u52A1\u6570\u636E"
columns:
- name: date
  type: datetime
  description: "\u65E5\u671F\u5B57\u6BB5 - date"
- name: revenue
  type: integer
  description: "\u6536\u5165 - revenue"
- name: category
  type: string
  description: "\u7C7B\u522B - category"
- name: region
  type: string
  description: "\u5730\u533A - region"
