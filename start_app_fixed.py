#!/usr/bin/env python3
"""
修复后的财务数据分析平台启动脚本
"""
import streamlit as st
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config_v3 import ConfigV3
from core.analysis_engine_v3 import AnalysisEngineV3
from utils.logger import setup_logger, get_logger

# 设置日志
setup_logger()
logger = get_logger(__name__)

def check_api_key():
    """检查API密钥配置"""
    if not ConfigV3.DASHSCOPE_API_KEY:
        st.error("⚠️ **API密钥未配置**")
        st.markdown("""
        ### 🔑 配置阿里云千问API密钥
        
        **方法1: 通过.env文件配置（推荐）**
        1. 在项目根目录创建或编辑 `.env` 文件
        2. 添加以下内容：
        ```
        DASHSCOPE_API_KEY=你的API密钥
        ```
        
        **方法2: 通过环境变量配置**
        ```bash
        export DASHSCOPE_API_KEY=你的API密钥
        ```
        
        **获取API密钥:**
        1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
        2. 登录并创建API密钥
        3. 复制密钥并按上述方法配置
        
        **配置完成后请重启应用**
        """)
        return False
    return True

def test_basic_functionality():
    """测试基本功能"""
    try:
        # 测试分析引擎初始化
        engine = AnalysisEngineV3()
        
        # 测试数据加载
        import pandas as pd
        test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02'],
            'amount': [1000, 2000]
        })
        
        success = engine.load_data(test_data, "test", "general")
        
        if success and engine.current_data is not None:
            return True, engine.llm_configured
        else:
            return False, False
            
    except Exception as e:
        logger.error(f"基本功能测试失败: {e}")
        return False, False

def main():
    """主函数"""
    st.set_page_config(
        page_title="财务数据分析平台 v3.0",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    st.title("🚀 财务数据分析平台 v3.0 启动检查")
    
    # 检查基本功能
    st.subheader("🔧 系统状态检查")
    
    with st.spinner("正在检查系统状态..."):
        basic_ok, llm_ok = test_basic_functionality()
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if basic_ok:
            st.success("✅ 基本功能正常")
        else:
            st.error("❌ 基本功能异常")
    
    with col2:
        if llm_ok:
            st.success("✅ AI功能正常")
        else:
            st.warning("⚠️ AI功能未配置")
    
    with col3:
        api_key_ok = check_api_key()
        if api_key_ok:
            st.success("✅ API密钥已配置")
        else:
            st.error("❌ API密钥未配置")
    
    # 显示功能状态
    st.subheader("📋 功能状态说明")
    
    if basic_ok:
        st.markdown("""
        ### ✅ 可用功能：
        - 📁 **数据上传和管理** - 支持 CSV, Excel 文件
        - 📊 **数据概览** - 查看数据基本信息、统计摘要
        - 📈 **基础分析** - 数据形状、列名、前几行等
        - 💾 **数据摘要** - 自动生成数据质量报告
        """)
        
        if llm_ok:
            st.markdown("""
            ### 🤖 AI增强功能：
            - 💬 **智能对话** - 自然语言查询数据
            - 🧠 **语义数据集** - 智能数据理解
            - 📊 **智能图表** - AI生成可视化
            - 🔍 **深度洞察** - 趋势分析、异常检测
            """)
        else:
            st.markdown("""
            ### ⚠️ AI功能需要配置：
            - 💬 智能对话功能
            - 🧠 语义数据集创建
            - 📊 AI图表生成
            - 🔍 深度数据洞察
            
            **配置API密钥后即可使用所有AI功能**
            """)
    
    # 启动按钮
    st.subheader("🚀 启动应用")
    
    if basic_ok:
        if st.button("🎯 启动财务数据分析平台", type="primary"):
            st.success("正在启动应用...")
            st.markdown("**请在新标签页中访问:** http://localhost:8502")
            
            # 启动主应用
            try:
                import subprocess
                subprocess.Popen([
                    sys.executable, "-m", "streamlit", "run", "app_v3.py", 
                    "--server.port", "8502"
                ])
                st.balloons()
            except Exception as e:
                st.error(f"启动失败: {e}")
                st.markdown("**手动启动命令:**")
                st.code("streamlit run app_v3.py --server.port 8502")
    else:
        st.error("❌ 系统检查未通过，无法启动应用")
    
    # 故障排除
    with st.expander("🔧 故障排除"):
        st.markdown("""
        ### 常见问题解决方案：
        
        **1. API密钥错误**
        - 检查密钥是否正确复制
        - 确认密钥是否有效（未过期）
        - 检查账户余额是否充足
        
        **2. 数据加载失败**
        - 检查文件格式是否支持（CSV, Excel）
        - 确认文件编码为UTF-8
        - 检查文件大小是否超限
        
        **3. 应用启动失败**
        - 检查端口8502是否被占用
        - 尝试使用不同端口：`streamlit run app_v3.py --server.port 8503`
        - 检查Python环境和依赖包
        
        **4. 获取帮助**
        - 查看日志文件：`app.log`
        - 检查控制台错误信息
        - 重启应用和浏览器
        """)

if __name__ == "__main__":
    main()
