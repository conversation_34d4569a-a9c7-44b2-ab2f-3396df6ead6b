#!/usr/bin/env python3
"""
测试PandasAI v3应用 - 验证第二阶段功能
"""
import pandas as pd
import os
import sys
from dotenv import load_dotenv

def test_app_v3_components():
    """测试v3应用组件"""
    print("=" * 60)
    print("🧪 测试v3应用组件")
    print("=" * 60)
    
    try:
        # 测试配置
        from config_v3 import ConfigV3
        print("✅ ConfigV3导入成功")
        
        # 测试分析引擎
        from core.analysis_engine_v3 import AnalysisEngineV3
        print("✅ AnalysisEngineV3导入成功")
        
        # 测试智能建议系统
        from core.smart_suggestions_v3 import SmartQuestionSuggester
        print("✅ SmartQuestionSuggester导入成功")
        
        # 测试数据管理器
        from core.data_manager import DataManager
        print("✅ DataManager导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 组件导入失败: {e}")
        return False

def test_smart_suggestions():
    """测试智能建议系统"""
    print("\n" + "=" * 60)
    print("💡 测试智能建议系统")
    print("=" * 60)
    
    try:
        from core.smart_suggestions_v3 import SmartQuestionSuggester
        
        suggester = SmartQuestionSuggester()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=10),
            'revenue': [1000, 1200, 1100, 1300, 1250, 1400, 1350, 1500, 1450, 1600],
            'category': ['A', 'B', 'A', 'B', 'A', 'B', 'A', 'B', 'A', 'B'],
            'region': ['北京', '上海', '北京', '上海', '北京', '上海', '北京', '上海', '北京', '上海']
        })
        
        print(f"📊 测试数据创建成功: {test_data.shape}")
        
        # 测试不同类型的建议
        dataset_types = ["revenue", "expenses", "balance_sheet", "general"]
        
        for dataset_type in dataset_types:
            suggestions = suggester.suggest_questions(test_data, dataset_type)
            print(f"\n📋 {dataset_type} 类型建议 ({len(suggestions)}个):")
            for i, suggestion in enumerate(suggestions[:3], 1):
                print(f"  {i}. {suggestion}")
        
        # 测试高级建议
        advanced_suggestions = suggester.get_advanced_suggestions(test_data, "revenue")
        print(f"\n🚀 高级建议 ({len(advanced_suggestions)}个):")
        for i, suggestion in enumerate(advanced_suggestions[:3], 1):
            print(f"  {i}. {suggestion}")
        
        return True
    except Exception as e:
        print(f"❌ 智能建议测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_engine_v3_features():
    """测试v3分析引擎特性"""
    print("\n" + "=" * 60)
    print("🧠 测试v3分析引擎特性")
    print("=" * 60)
    
    try:
        from core.analysis_engine_v3 import AnalysisEngineV3
        
        engine = AnalysisEngineV3()
        print("✅ 分析引擎创建成功")
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=5),
            'sales': [1000, 1200, 1100, 1300, 1250],
            'product': ['A', 'B', 'A', 'B', 'A'],
            'region': ['北京', '上海', '广州', '深圳', '北京']
        })
        
        # 测试数据加载（使用符合v3规范的名称）
        success = engine.load_data(test_data, "test-sales", "revenue")
        if success:
            print("✅ 数据加载成功")
        else:
            print("⚠️ 数据加载部分成功（可能语义数据集创建失败）")
        
        # 测试数据摘要
        summary = engine.get_data_summary()
        if summary["type"] != "error":
            print("✅ 数据摘要生成成功")
            print(f"📝 摘要预览: {summary['content'][:100]}...")
        else:
            print(f"❌ 数据摘要失败: {summary['content']}")
        
        # 测试可用数据集
        datasets = engine.get_available_datasets()
        print(f"📊 可用数据集: {datasets}")
        
        # 测试基本分析
        test_questions = [
            "数据有多少行？",
            "显示前3行数据"
        ]
        
        for question in test_questions:
            print(f"\n🤔 测试问题: {question}")
            result = engine.analyze(question, "test-sales" if datasets else None)
            
            if result["type"] == "error":
                print(f"⚠️ 分析结果: {result['content'][:100]}...")
            else:
                print(f"✅ 分析成功，类型: {result['type']}")
        
        return True
    except Exception as e:
        print(f"❌ 分析引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_v3_features():
    """测试v3配置特性"""
    print("\n" + "=" * 60)
    print("⚙️ 测试v3配置特性")
    print("=" * 60)
    
    try:
        from config_v3 import ConfigV3
        
        # 测试配置验证
        ConfigV3.validate_config()
        print("✅ 配置验证通过")
        
        # 测试财务模式获取
        for schema_type in ["revenue", "expenses", "balance_sheet", "general"]:
            schema = ConfigV3.get_financial_schema(schema_type)
            print(f"📋 {schema_type}: {schema['description']}")
        
        # 测试PandasAI配置
        config = ConfigV3.PANDASAI_CONFIG
        print(f"🤖 PandasAI配置: {config}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_streamlit_app_import():
    """测试Streamlit应用导入"""
    print("\n" + "=" * 60)
    print("🌐 测试Streamlit应用导入")
    print("=" * 60)
    
    try:
        # 尝试导入应用（不运行）
        import app_v3
        print("✅ app_v3.py导入成功")
        
        # 检查主要类
        app_class = getattr(app_v3, 'DataAnalysisAppV3', None)
        if app_class:
            print("✅ DataAnalysisAppV3类存在")
        else:
            print("❌ DataAnalysisAppV3类不存在")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Streamlit应用导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 PandasAI v3 第二阶段测试开始")
    print("测试语义层、智能建议和界面增强功能")
    
    # 加载环境变量
    load_dotenv()
    
    # 执行测试
    tests = [
        ("v3应用组件", test_app_v3_components),
        ("智能建议系统", test_smart_suggestions),
        ("v3分析引擎特性", test_analysis_engine_v3_features),
        ("v3配置特性", test_config_v3_features),
        ("Streamlit应用导入", test_streamlit_app_import)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 异常: {e}")
            results[test_name] = False
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📋 第二阶段测试总结")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed >= 4:  # 至少4个测试通过
        print("🎉 第二阶段测试基本通过！语义层和界面增强功能正常。")
        print("\n📋 下一步建议:")
        print("1. 运行 streamlit run app_v3.py 启动v3应用")
        print("2. 上传测试数据验证完整功能")
        print("3. 测试智能问题建议和语义数据集")
        return True
    else:
        print("💥 部分测试失败，需要检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
