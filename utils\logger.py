"""
日志工具模块
"""
import logging
import os
from config_v3 import ConfigV3

def setup_logger():
    """设置应用日志"""
    # 创建日志目录
    log_dir = os.path.dirname(ConfigV3.LOG_FILE) if os.path.dirname(ConfigV3.LOG_FILE) else "."
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 创建文件处理器
    file_handler = logging.FileHandler(ConfigV3.LOG_FILE, encoding='utf-8')
    file_handler.setFormatter(formatter)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 配置根日志器
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, ConfigV3.LOG_LEVEL))
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def get_logger(name):
    """获取指定名称的日志器"""
    return logging.getLogger(name)
