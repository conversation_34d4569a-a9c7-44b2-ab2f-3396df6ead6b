#!/usr/bin/env python3
"""
测试修复后的财务数据分析平台功能
"""
import pandas as pd
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config_v3 import ConfigV3
from core.analysis_engine_v3 import AnalysisEngineV3
from core.data_manager import DataManager
from utils.logger import setup_logger, get_logger

# 设置日志
setup_logger()
logger = get_logger(__name__)

def test_config():
    """测试配置"""
    print("🔧 测试配置...")
    try:
        ConfigV3.validate_config()
        print(f"✅ 配置验证成功")
        print(f"   - API密钥: {'已设置' if ConfigV3.DASHSCOPE_API_KEY else '未设置'}")
        print(f"   - LLM模型: {ConfigV3.LLM_MODEL}")
        print(f"   - 温度: {ConfigV3.LLM_TEMPERATURE}")
        return True
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def test_analysis_engine():
    """测试分析引擎"""
    print("\n🧠 测试分析引擎...")
    try:
        engine = AnalysisEngineV3()
        print(f"✅ 分析引擎初始化成功")
        print(f"   - LLM配置状态: {'已配置' if engine.llm_configured else '未配置'}")
        return engine
    except Exception as e:
        print(f"❌ 分析引擎初始化失败: {e}")
        return None

def test_data_loading(engine):
    """测试数据加载"""
    print("\n📊 测试数据加载...")
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'revenue': [10000, 12000, 11000],
            'category': ['产品A', '产品B', '产品A'],
            'region': ['北京', '上海', '广州']
        })
        
        # 测试数据加载
        success = engine.load_data(test_data, "test-dataset", "revenue")
        
        if success:
            print(f"✅ 数据加载成功")
            print(f"   - 数据形状: {engine.current_data.shape if engine.current_data is not None else 'None'}")
            print(f"   - 语义数据集: {list(engine.semantic_datasets.keys())}")
            return True
        else:
            print(f"❌ 数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        return False

def test_llm_connection(engine):
    """测试LLM连接"""
    print("\n🤖 测试LLM连接...")
    try:
        result = engine.validate_llm_connection()
        
        if result["type"] == "error":
            print(f"❌ LLM连接失败: {result['content']}")
            return False
        else:
            print(f"✅ LLM连接成功")
            print(f"   - 测试结果: {result['content']}")
            return True
            
    except Exception as e:
        print(f"❌ LLM连接测试失败: {e}")
        return False

def test_data_analysis(engine):
    """测试数据分析"""
    print("\n📈 测试数据分析...")
    try:
        # 测试简单查询
        result = engine.analyze("显示数据的前3行", "test-dataset")
        
        if result["type"] == "error":
            print(f"❌ 数据分析失败: {result['content']}")
            return False
        else:
            print(f"✅ 数据分析成功")
            print(f"   - 结果类型: {result['type']}")
            return True
            
    except Exception as e:
        print(f"❌ 数据分析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试财务数据分析平台修复...")
    
    # 测试配置
    if not test_config():
        print("\n❌ 配置测试失败，停止测试")
        return
    
    # 测试分析引擎
    engine = test_analysis_engine()
    if engine is None:
        print("\n❌ 分析引擎测试失败，停止测试")
        return
    
    # 测试数据加载
    if not test_data_loading(engine):
        print("\n❌ 数据加载测试失败")
    
    # 测试LLM连接（如果配置了）
    if engine.llm_configured:
        if not test_llm_connection(engine):
            print("\n❌ LLM连接测试失败")
        else:
            # 测试数据分析
            test_data_analysis(engine)
    else:
        print("\n⚠️ LLM未配置，跳过连接和分析测试")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
