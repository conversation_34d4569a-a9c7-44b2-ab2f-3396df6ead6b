#!/usr/bin/env python3
"""
通用财务查询系统测试脚本
验证修复后的通用查询功能
"""

import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.analysis_engine_v3 import AnalysisEngineV3
from config_v3 import ConfigV3

def test_query_enhancement():
    """测试查询增强功能"""
    print("🔍 测试查询增强功能...")
    
    engine = AnalysisEngineV3()
    
    # 测试查询增强
    test_queries = [
        "九州铁建2024年3月与2023年3月的总收入对比",
        "分析某公司的利润变化",
        "对比不同年份的成本数据"
    ]
    
    for query in test_queries:
        enhanced = engine._enhance_financial_query(query)
        print(f"原查询: {query}")
        print(f"增强后: {enhanced[:100]}...")
        print("-" * 50)

def test_context_creation():
    """测试上下文创建功能"""
    print("🔍 测试上下文创建功能...")
    
    try:
        engine = AnalysisEngineV3()
        
        # 加载数据
        df = pd.read_csv("financial_data.csv")
        result = engine.load_data(df, "财务数据测试")
        if not result:
            print(f"❌ 数据加载失败")
            return False
        
        # 创建上下文
        context = engine._create_financial_context()
        print("📋 数据上下文:")
        print(context)
        
        return True
        
    except Exception as e:
        print(f"❌ 上下文创建失败: {str(e)}")
        return False

def test_universal_queries():
    """测试通用查询功能"""
    print("🔍 测试通用查询功能...")
    
    try:
        engine = AnalysisEngineV3()
        
        # 加载数据
        df = pd.read_csv("financial_data.csv")
        result = engine.load_data(df, "财务数据")
        if not result:
            print(f"❌ 数据加载失败")
            return False
        
        print("✅ 数据加载成功")
        
        # 测试不同类型的查询
        test_queries = [
            "九州铁建2024年3月与2023年3月的营业总收入对比分析",
            "显示前5行数据",
            "数据统计摘要",
            "列名信息"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📋 测试查询 {i}: {query}")
            result = engine.analyze(query)
            
            if result["type"] == "error":
                print(f"❌ 查询失败: {result['content']}")
            else:
                print(f"✅ 查询成功，类型: {result['type']}")
                if result["type"] == "text":
                    print(f"结果预览: {result['content'][:200]}...")
                elif result["type"] == "dataframe":
                    print(f"返回数据框，形状: {result['content'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 通用查询测试失败: {str(e)}")
        return False

def verify_llm_configuration():
    """验证LLM配置"""
    print("🔍 验证LLM配置...")
    
    try:
        engine = AnalysisEngineV3()
        
        if engine.llm_configured:
            print("✅ LLM配置成功")
            
            # 测试连接
            result = engine.validate_llm_connection()
            if result["type"] == "error":
                print(f"⚠️ LLM连接测试失败: {result['content']}")
                print("这可能是网络问题或API密钥问题")
            else:
                print("✅ LLM连接测试成功")
        else:
            print("❌ LLM未配置")
            
        return True
        
    except Exception as e:
        print(f"❌ LLM配置验证失败: {str(e)}")
        return False

def analyze_data_structure():
    """分析数据结构"""
    print("🔍 分析数据结构...")
    
    try:
        df = pd.read_csv("financial_data.csv")
        
        print(f"📊 数据基本信息:")
        print(f"  - 数据形状: {df.shape}")
        print(f"  - 列名: {list(df.columns)}")
        
        # 分析关键列的数据
        key_columns = ['公司名称', '年份', '月份', '报表类型', '科目名称']
        for col in key_columns:
            if col in df.columns:
                unique_values = df[col].unique()
                print(f"  - {col}: {len(unique_values)} 个唯一值")
                if len(unique_values) <= 10:
                    print(f"    值: {list(unique_values)}")
                else:
                    print(f"    示例: {list(unique_values[:5])}...")
        
        # 检查九州铁建的数据
        if '公司名称' in df.columns:
            jiuzhou_data = df[df['公司名称'] == '九州铁建']
            print(f"\n📋 九州铁建数据:")
            print(f"  - 总记录数: {len(jiuzhou_data)}")
            
            if '年份' in df.columns and '月份' in df.columns:
                years_months = jiuzhou_data[['年份', '月份']].drop_duplicates()
                print(f"  - 时间范围: {len(years_months)} 个年月组合")
                
            if '科目名称' in df.columns:
                subjects = jiuzhou_data['科目名称'].unique()
                print(f"  - 科目数量: {len(subjects)}")
                if '营业总收入' in subjects:
                    revenue_data = jiuzhou_data[jiuzhou_data['科目名称'] == '营业总收入']
                    print(f"  - 营业总收入记录: {len(revenue_data)} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据结构分析失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 通用财务查询系统测试")
    print("=" * 60)
    
    # 验证配置
    try:
        ConfigV3.validate_config()
        print("✅ 配置验证通过")
    except Exception as e:
        print(f"❌ 配置验证失败: {str(e)}")
        print("请检查.env文件中的API密钥配置")
    
    print("\n" + "=" * 60)
    
    # 分析数据结构
    analyze_data_structure()
    
    print("\n" + "=" * 60)
    
    # 验证LLM配置
    verify_llm_configuration()
    
    print("\n" + "=" * 60)
    
    # 测试查询增强
    test_query_enhancement()
    
    print("\n" + "=" * 60)
    
    # 测试上下文创建
    test_context_creation()
    
    print("\n" + "=" * 60)
    
    # 测试通用查询
    if test_universal_queries():
        print("\n🎉 通用查询系统测试完成!")
    else:
        print("\n❌ 通用查询系统测试失败")

if __name__ == "__main__":
    main()
