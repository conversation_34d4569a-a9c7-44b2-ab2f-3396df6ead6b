#!/usr/bin/env python3
"""
九州铁建查询测试脚本
用于验证修复后的查询功能
"""

import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.analysis_engine_v3 import AnalysisEngineV3
from config_v3 import ConfigV3

def test_jiuzhou_query():
    """测试九州铁建查询功能"""
    print("🔍 开始测试九州铁建查询功能...")
    
    try:
        # 初始化分析引擎
        engine = AnalysisEngineV3()
        
        # 加载数据
        print("📊 加载财务数据...")
        result = engine.load_data("financial_data.csv", "九州铁建财务数据")
        
        if result["type"] == "error":
            print(f"❌ 数据加载失败: {result['content']}")
            return False
        
        print("✅ 数据加载成功")
        
        # 测试查询
        query = "请分析九州铁建2024年3月与2023年3月的总收入"
        print(f"🤖 执行查询: {query}")
        
        result = engine.analyze(query)
        
        if result["type"] == "error":
            print(f"❌ 查询失败: {result['content']}")
            return False
        
        print("✅ 查询成功!")
        print("📋 查询结果:")
        print(result["content"])
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

def verify_data_accuracy():
    """验证数据准确性"""
    print("\n🔍 验证数据准确性...")
    
    try:
        # 直接读取CSV文件进行验证
        df = pd.read_csv("financial_data.csv")
        
        # 筛选九州铁建营业总收入数据
        jiuzhou_data = df[
            (df['公司名称'] == '九州铁建') &
            (df['报表类型'] == '利润表') &
            (df['科目名称'] == '营业总收入') &
            (df['月份'] == 3) &
            (df['年份'].isin([2023, 2024]))
        ]
        
        print(f"📊 找到 {len(jiuzhou_data)} 条相关数据")
        
        if not jiuzhou_data.empty:
            print("\n📋 详细数据:")
            for _, row in jiuzhou_data.iterrows():
                print(f"  {row['年份']}年{row['月份']}月: {row['本月数']:,.2f} 元")
        
        # 计算对比
        data_2023 = jiuzhou_data[jiuzhou_data['年份'] == 2023]['本月数'].sum()
        data_2024 = jiuzhou_data[jiuzhou_data['年份'] == 2024]['本月数'].sum()
        
        print(f"\n📈 对比结果:")
        print(f"  2023年3月: {data_2023:,.2f} 元")
        print(f"  2024年3月: {data_2024:,.2f} 元")
        
        if data_2023 > 0:
            growth_rate = ((data_2024 - data_2023) / data_2023) * 100
            print(f"  增长率: {growth_rate:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据验证失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 九州铁建查询修复验证")
    print("=" * 50)
    
    # 验证配置
    try:
        ConfigV3.validate_config()
        print("✅ 配置验证通过")
    except Exception as e:
        print(f"❌ 配置验证失败: {str(e)}")
        return
    
    # 验证数据准确性
    if not verify_data_accuracy():
        return
    
    # 测试查询功能
    if test_jiuzhou_query():
        print("\n🎉 所有测试通过!")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
